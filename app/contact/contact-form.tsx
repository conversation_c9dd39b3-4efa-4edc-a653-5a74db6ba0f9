"use client";

import { useState } from "react";
import { CyberpunkBackground } from "@/components/cyberpunk-background";
import { Navbar } from "@/components/navbar";
import { CloseButton } from "@/components/close-button";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { ArrowRight, Mail, MessageSquare, Phone } from "lucide-react";

export default function ContactForm() {
    const { toast } = useToast();
    const [formData, setFormData] = useState({
        name: "",
        email: "",
        phone: "",
        company: "",
        message: "",
    });
    const [isSubmitting, setIsSubmitting] = useState(false);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name]: value }));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);

        setTimeout(() => {
            setIsSubmitting(false);
            toast({
                title: "Request received!",
                description: "We'll contact you shortly to schedule a call.",
            });
            setFormData({
                name: "",
                email: "",
                phone: "",
                company: "",
                message: "",
            });
        }, 1500);
    };

    return (
        <main className="relative min-h-screen bg-zinc-950 text-white">
            <CyberpunkBackground />
            <Navbar />
            <CloseButton />

            <div className="relative z-10 pt-20 pb-32 px-6">
                <div className="max-w-7xl mx-auto">
                    <h1 className="text-6xl md:text-7xl font-bold mb-8 glitch-text" data-text="Request a Call">
                        Request a Call
                    </h1>
                    <p className="text-xl md:text-2xl text-zinc-300 mb-16 max-w-3xl cyber-text">
                        Ready to transform your business with intelligent automation? Let&apos;s schedule a call.
                    </p>

                    <div className="grid md:grid-cols-2 gap-12">
                        <Card className="p-8 border border-primary/20 bg-background/20 backdrop-blur-sm">
                            <h2 className="text-2xl font-bold mb-6 cyber-text">Contact Information</h2>

                            <div className="space-y-6">
                                <div className="flex items-start">
                                    <Mail className="h-6 w-6 text-primary mr-4 mt-1" />
                                    <div>
                                        <h3 className="font-medium">Email</h3>
                                        <p className="text-zinc-400"><EMAIL></p>
                                    </div>
                                </div>

                                <div className="flex items-start">
                                    <Phone className="h-6 w-6 text-primary mr-4 mt-1" />
                                    <div>
                                        <h3 className="font-medium">Phone</h3>
                                        <p className="text-zinc-400">+****************</p>
                                    </div>
                                </div>

                                <div className="flex items-start">
                                    <MessageSquare className="h-6 w-6 text-primary mr-4 mt-1" />
                                    <div>
                                        <h3 className="font-medium">Support Hours</h3>
                                        <p className="text-zinc-400">24/7 availability for enterprise clients</p>
                                    </div>
                                </div>
                            </div>
                        </Card>

                        <form onSubmit={handleSubmit} className="space-y-6">
                            <div className="grid md:grid-cols-2 gap-6">
                                <div className="space-y-2">
                                    <label htmlFor="name" className="text-sm font-medium">
                                        Name
                                    </label>
                                    <Input
                                        id="name"
                                        name="name"
                                        value={formData.name}
                                        onChange={handleChange}
                                        className="bg-background/20 border-primary/20 focus:border-primary/40"
                                        placeholder="John Smith"
                                        required
                                    />
                                </div>
                                <div className="space-y-2">
                                    <label htmlFor="email" className="text-sm font-medium">
                                        Email
                                    </label>
                                    <Input
                                        id="email"
                                        name="email"
                                        type="email"
                                        value={formData.email}
                                        onChange={handleChange}
                                        className="bg-background/20 border-primary/20 focus:border-primary/40"
                                        placeholder="<EMAIL>"
                                        required
                                    />
                                </div>
                            </div>
                            <div className="grid md:grid-cols-2 gap-6">
                                <div className="space-y-2">
                                    <label htmlFor="phone" className="text-sm font-medium">
                                        Phone
                                    </label>
                                    <Input
                                        id="phone"
                                        name="phone"
                                        type="tel"
                                        value={formData.phone}
                                        onChange={handleChange}
                                        className="bg-background/20 border-primary/20 focus:border-primary/40"
                                        placeholder="+****************"
                                        required
                                    />
                                </div>
                                <div className="space-y-2">
                                    <label htmlFor="company" className="text-sm font-medium">
                                        Company
                                    </label>
                                    <Input
                                        id="company"
                                        name="company"
                                        value={formData.company}
                                        onChange={handleChange}
                                        className="bg-background/20 border-primary/20 focus:border-primary/40"
                                        placeholder="Your company name"
                                    />
                                </div>
                            </div>
                            <div className="space-y-2">
                                <label htmlFor="message" className="text-sm font-medium">
                                    Message
                                </label>
                                <Textarea
                                    id="message"
                                    name="message"
                                    value={formData.message}
                                    onChange={handleChange}
                                    className="bg-background/20 border-primary/20 focus:border-primary/40"
                                    placeholder="Tell us about your project and requirements..."
                                    rows={5}
                                    required
                                />
                            </div>
                            <Button
                                type="submit"
                                disabled={isSubmitting}
                                className="w-full group relative bg-primary/60 backdrop-blur-sm border border-primary/50 hover:bg-primary/80 hover:border-primary transition-all duration-300"
                            >
                                <div className="absolute inset-0 bg-primary/20 group-hover:bg-primary/40 transform skew-x-12 transition-all duration-300" />
                                <span className="relative z-10">
                                    {isSubmitting ? "Sending..." : "Schedule Call"}
                                </span>
                                <ArrowRight className="ml-2 h-4 w-4 relative z-10 transition-transform group-hover:translate-x-1" />
                            </Button>
                        </form>
                    </div>
                </div>
            </div>
        </main>
    );
} 