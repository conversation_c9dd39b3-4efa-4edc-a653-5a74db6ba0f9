import './globals.css';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { ThemeProvider } from '@/components/theme-provider';
import { Toaster } from '@/components/ui/toaster';

const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  preload: true,
});

export const metadata: Metadata = {
  title: {
    default: 'NexusAI - Automation & AI Solutions',
    template: '%s | NexusAI'
  },
  description: 'Transform your business with next-generation automation and AI agent solutions. Intelligent automation, advanced AI systems, and cutting-edge technology for modern enterprises.',
  keywords: [
    'AI automation',
    'artificial intelligence',
    'business automation',
    'AI agents',
    'machine learning',
    'intelligent systems',
    'digital transformation',
    'enterprise AI',
    'automation solutions',
    'AI technology'
  ],
  authors: [{ name: 'NexusAI Team' }],
  creator: 'NexusAI',
  publisher: 'NexusAI',
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://nexusai.com',
    title: 'NexusAI - Automation & AI Solutions',
    description: 'Transform your business with next-generation automation and AI agent solutions. Intelligent automation for modern enterprises.',
    siteName: 'NexusAI',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'NexusAI - Automation & AI Solutions',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'NexusAI - Automation & AI Solutions',
    description: 'Transform your business with next-generation automation and AI agent solutions.',
    images: ['/og-image.jpg'],
    creator: '@nexusai',
  },
  metadataBase: new URL('https://nexusai.com'),
  alternates: {
    canonical: '/',
  },
  category: 'technology',
  verification: {
    google: 'your-google-verification-code',
    yandex: 'your-yandex-verification-code',
    yahoo: 'your-yahoo-verification-code',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        <meta name="theme-color" content="#000000" />
        <meta name="msapplication-TileColor" content="#000000" />
        <meta name="format-detection" content="telephone=no" />
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Organization",
              "name": "NexusAI",
              "url": "https://nexusai.com",
              "logo": "https://nexusai.com/logo.png",
              "description": "Transform your business with next-generation automation and AI agent solutions",
              "sameAs": [
                "https://twitter.com/nexusai",
                "https://linkedin.com/company/nexusai"
              ],
              "contactPoint": {
                "@type": "ContactPoint",
                "telephone": "******-0123",
                "contactType": "customer service"
              }
            })
          }}
        />
      </head>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="dark" enableSystem>
          {children}
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  );
}