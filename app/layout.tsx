import './globals.css';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { ThemeProvider } from '@/components/theme-provider';
import { Toaster } from '@/components/ui/toaster';

const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  preload: true,
});

export const metadata: Metadata = {
  title: {
    default: 'vruha studio - AI Automation & Services',
    template: '%s | vruha studio'
  },
  description: 'Professional AI automation and services by vruha studio. We deliver cutting-edge AI solutions, intelligent automation, and expert AI services to transform your business operations.',
  keywords: [
    'AI automation',
    'AI services',
    'artificial intelligence',
    'business automation',
    'AI consulting',
    'machine learning',
    'intelligent systems',
    'digital transformation',
    'automation services',
    'AI solutions'
  ],
  authors: [{ name: 'vruha studio Team' }],
  creator: 'vruha studio',
  publisher: 'vruha studio',
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://vruhastudio.com',
    title: 'vruha studio - AI Automation & Services',
    description: 'Professional AI automation and services by vruha studio. We deliver cutting-edge AI solutions and expert services to transform your business operations.',
    siteName: 'vruha studio',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'vruha studio - AI Automation & Services',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'vruha studio - AI Automation & Services',
    description: 'Professional AI automation and services by vruha studio. We deliver cutting-edge AI solutions and expert services.',
    images: ['/og-image.jpg'],
    creator: '@vruhastudio',
  },
  metadataBase: new URL('https://vruhastudio.com'),
  alternates: {
    canonical: '/',
  },
  category: 'technology',
  verification: {
    google: 'your-google-verification-code',
    yandex: 'your-yandex-verification-code',
    yahoo: 'your-yahoo-verification-code',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        <meta name="theme-color" content="#000000" />
        <meta name="msapplication-TileColor" content="#000000" />
        <meta name="format-detection" content="telephone=no" />
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Organization",
              "name": "vruha studio",
              "url": "https://vruhastudio.com",
              "logo": "https://vruhastudio.com/logo.png",
              "description": "Professional AI automation and services. We deliver cutting-edge AI solutions and expert services to transform your business operations.",
              "sameAs": [
                "https://twitter.com/vruhastudio",
                "https://linkedin.com/company/vruhastudio"
              ],
              "contactPoint": {
                "@type": "ContactPoint",
                "telephone": "******-0123",
                "contactType": "customer service"
              }
            })
          }}
        />
      </head>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="dark" enableSystem>
          {children}
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  );
}