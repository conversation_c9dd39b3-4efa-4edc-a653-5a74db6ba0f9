import { CyberpunkBackground } from "@/components/cyberpunk-background";
import { Navbar } from "@/components/navbar";
import { CloseButton } from "@/components/close-button";
import { Card } from "@/components/ui/card";
import { Bot, Workflow, Database, Zap, BarChart, CloudCog } from "lucide-react";
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'AI Services - Automation & Intelligent Solutions',
  description: 'Comprehensive AI and automation solutions including AI agents, workflow automation, data processing, API integration, analytics dashboards, and cloud solutions.',
  keywords: ['AI services', 'automation solutions', 'AI agents', 'workflow automation', 'data processing', 'API integration', 'analytics dashboard', 'cloud solutions'],
  openGraph: {
    title: 'AI Services - Automation & Intelligent Solutions',
    description: 'Comprehensive AI and automation solutions designed to transform your business operations.',
    url: '/services',
  },
  twitter: {
    title: 'AI Services - Automation & Intelligent Solutions',
    description: 'Comprehensive AI and automation solutions designed to transform your business operations.',
  },
};

const services = [
  {
    title: "AI Agents",
    description: "Autonomous AI agents that learn, adapt, and execute complex tasks across your organization.",
    icon: Bo<PERSON>,
  },
  {
    title: "Workflow Automation",
    description: "Streamline repetitive tasks and build intelligent workflows that scale with your business.",
    icon: Workflow,
  },
  {
    title: "Data Processing",
    description: "Transform raw data into actionable insights with our intelligent processing pipeline.",
    icon: Database,
  },
  {
    title: "API Integration",
    description: "Seamlessly connect with existing systems and third-party services for unified operations.",
    icon: Zap,
  },
  {
    title: "Analytics Dashboard",
    description: "Visualize performance metrics and gain insights with customizable real-time dashboards.",
    icon: BarChart,
  },
  {
    title: "Cloud Solutions",
    description: "Scalable and secure cloud infrastructure optimized for AI and automation workloads.",
    icon: CloudCog,
  },
];

export default function Services() {
  return (
    <main className="relative min-h-screen bg-zinc-950 text-white">
      <CyberpunkBackground />
      <Navbar />
      <CloseButton />

      <div className="relative z-10 pt-20 pb-32 px-6">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-6xl md:text-7xl font-bold mb-8 glitch-text" data-text="Our Services">
            Our Services
          </h1>
          <p className="text-xl md:text-2xl text-zinc-300 mb-16 max-w-3xl cyber-text">
            Comprehensive AI and automation solutions designed to transform your business operations.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <div key={index} className="group animate-in" style={{ animationDelay: `${index * 150}ms` }}>
                <Card className="h-full border border-blue-500/20 bg-background/20 backdrop-blur-sm hover:border-blue-500/40 hover:bg-blue-500/10 transition-all duration-300">
                  <div className="p-6">
                    <div className="mb-4 p-3 w-12 h-12 rounded-lg bg-blue-500/20 flex items-center justify-center group-hover:bg-blue-500/30 transition-colors">
                      <service.icon className="h-6 w-6 text-blue-400" />
                    </div>
                    <h3 className="text-xl font-semibold mb-3 cyber-text">{service.title}</h3>
                    <p className="text-zinc-400 group-hover:text-zinc-300 transition-colors">
                      {service.description}
                    </p>
                  </div>
                </Card>
              </div>
            ))}
          </div>
        </div>
      </div>
    </main>
  );
}