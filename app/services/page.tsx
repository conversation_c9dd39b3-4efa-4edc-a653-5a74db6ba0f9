import { CyberpunkBackground } from "@/components/cyberpunk-background";
import { Navbar } from "@/components/navbar";
import { CloseButton } from "@/components/close-button";
import { Card } from "@/components/ui/card";
import { Bot, Share2, Code, Shield } from "lucide-react";
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'AI Services - vruha studio',
  description: 'Professional AI automation services: automate business processes, social media management, rapid software development, and automated security solutions.',
  keywords: ['AI automation', 'business process automation', 'social media automation', 'rapid software development', 'automated security', 'AI agents', 'vruha studio'],
  openGraph: {
    title: 'AI Services - vruha studio',
    description: 'Professional AI automation services to transform your business operations and save time on repetitive tasks.',
    url: '/services',
  },
  twitter: {
    title: 'AI Services - vruha studio',
    description: 'Professional AI automation services to transform your business operations and save time on repetitive tasks.',
  },
};

const services = [
  {
    title: "Business Process Automation",
    description: "Automate boring business processes with AI agents to help save time and resources on repetitive tasks. Let intelligent automation handle routine work while you focus on what matters most.",
    icon: Bo<PERSON>,
  },
  {
    title: "Social Media Automation",
    description: "Use prebuilt AI agents to automate your social media tasks and monitor online presence. Run efficient ad campaigns and maintain consistent engagement across all platforms.",
    icon: Share2,
  },
  {
    title: "Rapid Software Development",
    description: "From MVP to complex custom needs, we build software quickly using latest AI tools and years of experience. Accelerate your development timeline without compromising quality.",
    icon: Code,
  },
  {
    title: "Automated Security",
    description: "Use AI to secure your infrastructure over internet. Analyze and monitor your infrastructure using agents, setup alert systems and dashboards to manage assets effectively.",
    icon: Shield,
  },
];

export default function Services() {
  return (
    <main className="relative min-h-screen bg-black text-white">
      <CyberpunkBackground />
      <Navbar />
      <CloseButton />

      <div className="relative z-10 pt-20 pb-32 px-6">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-6xl md:text-7xl font-bold mb-8 glitch-text" data-text="Our Services">
            Our Services
          </h1>
          <p className="text-xl md:text-2xl text-white mb-16 max-w-3xl cyber-text">
            Professional AI automation services designed to save time, reduce costs, and transform how you work.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {services.map((service, index) => (
              <div key={index} className="group animate-in" style={{ animationDelay: `${index * 150}ms` }}>
                <Card className="h-full border border-gray-800 bg-gray-900/50 backdrop-blur-sm hover:border-gray-700 hover:bg-gray-800/50 transition-all duration-300">
                  <div className="p-6">
                    <div className="mb-4 p-3 w-12 h-12 rounded-lg bg-gray-800 flex items-center justify-center group-hover:bg-gray-700 transition-colors">
                      <service.icon className="h-6 w-6 text-cyan-400" />
                    </div>
                    <h3 className="text-xl font-semibold mb-3 text-white">{service.title}</h3>
                    <p className="text-gray-300 group-hover:text-white transition-colors">
                      {service.description}
                    </p>
                  </div>
                </Card>
              </div>
            ))}
          </div>
        </div>
      </div>
    </main>
  );
}