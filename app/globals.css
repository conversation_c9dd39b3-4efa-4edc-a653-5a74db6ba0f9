@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

/* Performance optimizations */
* {
  box-sizing: border-box;
}

html {
  font-display: swap;
  scroll-behavior: smooth;
}

body {
  font-display: swap;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeSpeed;
  background: #0a0a0a;
  color: #ffffff;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 221 83% 53%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 262 83% 58%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 221 83% 53%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 224 71% 4%;
    --foreground: 0 0% 98%;
    --card: 224 71% 4%;
    --card-foreground: 0 0% 98%;
    --popover: 224 71% 4%;
    --popover-foreground: 0 0% 98%;
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 98%;
    --secondary: 217 19% 27%;
    --secondary-foreground: 0 0% 98%;
    --muted: 217 19% 27%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 260 100% 67%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 217 19% 27%;
    --input: 217 19% 27%;
    --ring: 224 76% 48%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

.animate-in {
  animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.glitch-text {
  position: relative;
  color: #ffffff;
  animation: subtle-glitch 5s infinite;
}

.glitch-text::before,
.glitch-text::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  pointer-events: none;
}

.glitch-text::before {
  animation: subtle-glitch-1 7s infinite;
}

.glitch-text::after {
  animation: subtle-glitch-2 9s infinite;
}

@keyframes subtle-glitch {

  0%,
  95% {
    text-shadow: 0 0 0 transparent;
  }

  96% {
    text-shadow: 0 0 8px rgba(0, 212, 255, 0.6);
  }

  98% {
    text-shadow: 0 0 4px rgba(255, 0, 128, 0.4);
  }

  100% {
    text-shadow: 0 0 0 transparent;
  }
}

@keyframes subtle-glitch-1 {

  0%,
  94% {
    opacity: 0;
    transform: translateX(0);
  }

  95% {
    opacity: 0.3;
    transform: translateX(0.8px);
    color: rgba(0, 212, 255, 0.7);
  }

  97% {
    opacity: 0;
    transform: translateX(0);
  }
}

@keyframes subtle-glitch-2 {

  0%,
  93% {
    opacity: 0;
    transform: translateX(0);
  }

  94% {
    opacity: 0.25;
    transform: translateX(-0.8px);
    color: rgba(255, 0, 128, 0.6);
  }

  96% {
    opacity: 0;
    transform: translateX(0);
  }
}

.cyber-text {
  color: #ffffff;
  text-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
  animation: cyber-glow 4s ease-in-out infinite alternate;
}

@keyframes cyber-glow {
  from {
    text-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
  }

  to {
    text-shadow: 0 0 8px rgba(0, 212, 255, 0.4);
  }
}