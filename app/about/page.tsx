import { CyberpunkBackground } from "@/components/cyberpunk-background";
import { Navbar } from "@/components/navbar";
import { CloseButton } from "@/components/close-button";
import { Card } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Award, Target, Users, Lightbulb } from "lucide-react";
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'About Us - vruha studio',
  description: 'Learn about vruha studio&apos;s mission to deliver professional AI automation and services. Meet our team and discover our commitment to innovation and excellence.',
  keywords: ['vruha studio team', 'AI automation experts', 'AI services', 'artificial intelligence company', 'business automation'],
  openGraph: {
    title: 'About vruha studio - Professional AI Services',
    description: 'Delivering professional AI automation and services with cutting-edge solutions.',
    url: '/about',
  },
  twitter: {
    title: 'About vruha studio - Professional AI Services',
    description: 'Delivering professional AI automation and services with cutting-edge solutions.',
  },
};

const team = [
  {
    name: "Dr. <PERSON>",
    role: "Chief AI Officer",
    image: "https://images.pexels.com/photos/3763188/pexels-photo-3763188.jpeg",
    bio: "Leading expert in artificial intelligence with over 15 years of experience in developing enterprise AI solutions.",
  },
  {
    name: "Michael Rodriguez",
    role: "Head of Innovation",
    image: "https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg",
    bio: "Pioneering new approaches to automation and AI integration in business processes.",
  },
  {
    name: "Emma Thompson",
    role: "Technical Director",
    image: "https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg",
    bio: "Architect of our core AI platform with expertise in scalable system design.",
  },
];

const values = [
  {
    title: "Innovation",
    description: "Pushing the boundaries of what's possible with AI and automation.",
    icon: Lightbulb,
  },
  {
    title: "Excellence",
    description: "Delivering exceptional quality in every solution we provide.",
    icon: Award,
  },
  {
    title: "Impact",
    description: "Creating meaningful change through technological advancement.",
    icon: Target,
  },
  {
    title: "Collaboration",
    description: "Working together to achieve extraordinary results.",
    icon: Users,
  },
];

export default function About() {
  return (
    <main className="relative min-h-screen bg-black text-white">
      <CyberpunkBackground />
      <Navbar />
      <CloseButton />

      <div className="relative z-10 pt-20 pb-32 px-6">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-6xl md:text-7xl font-bold mb-8 glitch-text" data-text="About Us">
            About Us
          </h1>
          <p className="text-xl md:text-2xl text-white mb-16 max-w-3xl cyber-text">
            Delivering professional AI automation and services with cutting-edge solutions.
          </p>

          <div className="grid md:grid-cols-2 gap-16 mb-24">
            <div>
              <h2 className="text-3xl font-bold mb-6 text-white">Our Mission</h2>
              <p className="text-lg text-gray-300 mb-8">
                We&apos;re on a mission to deliver professional AI automation and services that transform how businesses operate. Our solutions empower organizations to achieve unprecedented efficiency and innovation.
              </p>
              <div className="grid grid-cols-2 gap-6">
                {values.map((value, index) => (
                  <Card key={index} className="border border-gray-800 bg-gray-900/50 backdrop-blur-sm p-4">
                    <value.icon className="h-6 w-6 text-cyan-400 mb-3" />
                    <h3 className="font-semibold mb-2 text-white">{value.title}</h3>
                    <p className="text-sm text-gray-300">{value.description}</p>
                  </Card>
                ))}
              </div>
            </div>

            <div className="space-y-6">
              <h2 className="text-3xl font-bold mb-6 text-white">Our Story</h2>
              <p className="text-lg text-gray-300">
                vruha studio emerged from a vision to make advanced AI automation and services accessible to businesses of all sizes. Our team of experts combines years of experience in artificial intelligence, automation, and enterprise software development.
              </p>
              <p className="text-lg text-gray-300">
                Today, we&apos;re proud to serve organizations worldwide, helping them transform their operations through intelligent automation and expert AI services. Our commitment to innovation and excellence drives us to continuously deliver cutting-edge solutions.
              </p>
            </div>
          </div>

          <h2 className="text-3xl font-bold mb-12 text-white text-center">Leadership Team</h2>
          <div className="grid md:grid-cols-3 gap-8">
            {team.map((member, index) => (
              <div key={index} className="text-center animate-in" style={{ animationDelay: `${index * 150}ms` }}>
                <Avatar className="w-32 h-32 mx-auto mb-6">
                  <AvatarImage src={member.image} alt={member.name} />
                  <AvatarFallback>{member.name[0]}</AvatarFallback>
                </Avatar>
                <h3 className="text-xl font-semibold mb-2 text-white">{member.name}</h3>
                <p className="text-cyan-400 mb-4">{member.role}</p>
                <p className="text-gray-300">{member.bio}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </main>
  );
}