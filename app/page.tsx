import { Navbar } from "@/components/navbar";
import { CyberpunkBackground } from "@/components/cyberpunk-background";
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'vruha studio - AI Automation & Services',
  description: 'Professional AI automation and services by vruha studio. We deliver cutting-edge AI solutions, intelligent automation, and expert AI services to transform your business operations.',
  keywords: ['AI automation', 'AI services', 'artificial intelligence', 'business automation', 'AI consulting', 'machine learning', 'AI solutions', 'automation services'],
  openGraph: {
    title: 'vruha studio - AI Automation & Services',
    description: 'Professional AI automation and services. We deliver cutting-edge AI solutions and expert services to transform your business operations.',
    url: '/',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'vruha studio - AI Automation & Services',
      },
    ],
  },
  twitter: {
    title: 'vruha studio - AI Automation & Services',
    description: 'Professional AI automation and services. We deliver cutting-edge AI solutions and expert services to transform your business operations.',
    images: ['/og-image.jpg'],
  },
};

export default function Home() {
  return (
    <main className="relative min-h-screen bg-black text-white">
      <CyberpunkBackground />
      <Navbar />

      <div className="relative z-10 pointer-events-none">
        <section className="h-screen flex items-center justify-center px-8">
          <div className="max-w-7xl pointer-events-auto">
            <h1 className="text-8xl md:text-9xl font-bold leading-tight mb-12 glitch-text" data-text="AI Automation">
              AI<br />Automation
            </h1>
            <p className="text-2xl md:text-3xl leading-relaxed max-w-4xl text-white font-medium animate-in cyber-text">
              vruha studio delivers professional AI automation and services that transform your business operations with intelligent solutions.
            </p>
          </div>
        </section>

        <div className="h-96 flex items-center justify-center px-8">
          <p className="text-3xl md:text-4xl text-center max-w-5xl text-gray-300 italic font-medium animate-in">
            &quot;Empowering businesses through intelligent AI automation and expert services.&quot;
          </p>
        </div>

        <section className="h-screen flex items-center justify-center px-8">
          <div className="max-w-7xl pointer-events-auto">
            <h1 className="text-8xl md:text-9xl font-bold leading-tight mb-12 glitch-text" data-text="Expert Services">
              Expert<br />Services
            </h1>
            <p className="text-2xl md:text-3xl leading-relaxed max-w-4xl text-white font-medium animate-in cyber-text">
              From AI consulting to custom automation solutions, we provide comprehensive services tailored to your unique business needs.
            </p>
          </div>
        </section>

        <div className="h-96 flex items-center justify-center px-8">
          <p className="text-3xl md:text-4xl text-center max-w-5xl text-gray-300 italic font-medium animate-in">
            &quot;Innovation through intelligent automation - that&apos;s the vruha studio difference.&quot;
          </p>
        </div>

        <section className="h-screen flex items-center justify-center px-8">
          <div className="max-w-7xl pointer-events-auto">
            <h1 className="text-8xl md:text-9xl font-bold leading-tight mb-12 glitch-text" data-text="Transform Today">
              Transform<br />Today
            </h1>
            <p className="text-2xl md:text-3xl leading-relaxed max-w-4xl text-white font-medium animate-in cyber-text">
              Ready to elevate your business with AI? Our automation services and AI expertise are designed to deliver measurable results.
            </p>
          </div>
        </section>
      </div>
    </main>
  );
}