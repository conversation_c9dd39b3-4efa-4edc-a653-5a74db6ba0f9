import { Navbar } from "@/components/navbar";
import { CyberpunkBackground } from "@/components/cyberpunk-background";
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'NexusAI - Intelligent Automation & AI Solutions',
  description: 'Experience the future of AI-powered automation with NexusAI. Intelligent agents that transform business processes with precision and efficiency. Advanced AI solutions for modern enterprises.',
  keywords: ['intelligent automation', 'AI solutions', 'artificial intelligence', 'business automation', 'AI agents', 'machine learning', 'enterprise AI', 'automated processes'],
  openGraph: {
    title: 'NexusAI - Intelligent Automation & AI Solutions',
    description: 'Experience the future of AI-powered automation, where intelligent agents transform your business processes with precision and efficiency.',
    url: '/',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'NexusAI - Intelligent Automation & AI Solutions',
      },
    ],
  },
  twitter: {
    title: 'NexusAI - Intelligent Automation & AI Solutions',
    description: 'Experience the future of AI-powered automation, where intelligent agents transform your business processes with precision and efficiency.',
    images: ['/og-image.jpg'],
  },
};

export default function Home() {
  return (
    <main className="relative min-h-screen bg-zinc-950 text-white">
      <CyberpunkBackground />
      <Navbar />

      <div className="relative z-10 pointer-events-none">
        <section className="h-screen flex items-center justify-center px-8">
          <div className="max-w-7xl pointer-events-auto">
            <h1 className="text-8xl md:text-9xl font-bold leading-tight mb-12 glitch-text" data-text="Intelligent Automation">
              Intelligent<br />Automation
            </h1>
            <p className="text-2xl md:text-3xl leading-relaxed max-w-4xl text-zinc-300 font-medium animate-in cyber-text">
              Experience the future of AI-powered automation, where intelligent agents transform your business processes with precision and efficiency.
            </p>
          </div>
        </section>

        <div className="h-96 flex items-center justify-center px-8">
          <p className="text-3xl md:text-4xl text-center max-w-5xl text-zinc-400 italic font-medium animate-in cyber-text">
            &quot;AI is not just about automation, it&apos;s about augmenting human potential.&quot;
          </p>
        </div>

        <section className="h-screen flex items-center justify-center px-8">
          <div className="max-w-7xl pointer-events-auto">
            <h1 className="text-8xl md:text-9xl font-bold leading-tight mb-12 glitch-text" data-text="Advanced Solutions">
              Advanced<br />Solutions
            </h1>
            <p className="text-2xl md:text-3xl leading-relaxed max-w-4xl text-zinc-300 font-medium animate-in cyber-text">
              Our AI agents learn, adapt, and execute complex tasks with unprecedented accuracy, scaling your operations seamlessly.
            </p>
          </div>
        </section>

        <div className="h-96 flex items-center justify-center px-8">
          <p className="text-3xl md:text-4xl text-center max-w-5xl text-zinc-400 italic font-medium animate-in cyber-text">
            &quot;The future belongs to those who automate intelligently.&quot;
          </p>
        </div>

        <section className="h-screen flex items-center justify-center px-8">
          <div className="max-w-7xl pointer-events-auto">
            <h1 className="text-8xl md:text-9xl font-bold leading-tight mb-12 glitch-text" data-text="Transform Today">
              Transform<br />Today
            </h1>
            <p className="text-2xl md:text-3xl leading-relaxed max-w-4xl text-zinc-300 font-medium animate-in cyber-text">
              Ready to revolutionize your business? Our AI solutions are designed to deliver immediate impact and long-term value.
            </p>
          </div>
        </section>
      </div>
    </main>
  );
}