import { CyberpunkBackground } from "@/components/cyberpunk-background";
import { Navbar } from "@/components/navbar";
import { CloseButton } from "@/components/close-button";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, Shield, Cpu, Brain, Rocket } from "lucide-react";
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'AI Products - Enterprise Solutions & Platforms',
  description: 'Discover NexusAI enterprise products: NexusCore AI platform, AutoPilot Pro workflow automation, SecureFlow data processing, and ScaleForce cloud solutions.',
  keywords: ['AI products', 'enterprise AI platform', 'workflow automation', 'data processing', 'cloud solutions', 'NexusCore AI', 'AutoPilot Pro', 'SecureFlow', 'ScaleForce'],
  openGraph: {
    title: 'AI Products - Enterprise Solutions & Platforms',
    description: 'Cutting-edge AI solutions engineered for enterprise performance and scalability.',
    url: '/products',
  },
  twitter: {
    title: 'AI Products - Enterprise Solutions & Platforms',
    description: 'Cutting-edge AI solutions engineered for enterprise performance and scalability.',
  },
};

const products = [
  {
    title: "NexusCore AI",
    description: "Enterprise-grade AI platform with advanced natural language processing and machine learning capabilities.",
    features: ["Multi-model support", "Real-time processing", "Custom training"],
    price: "$999/mo",
    icon: Brain,
  },
  {
    title: "AutoPilot Pro",
    description: "Automated workflow solution that streamlines business processes and enhances productivity.",
    features: ["Visual workflow builder", "Integration hub", "Performance analytics"],
    price: "$499/mo",
    icon: Cpu,
  },
  {
    title: "SecureFlow",
    description: "End-to-end encrypted data processing pipeline with military-grade security.",
    features: ["Zero-trust architecture", "Audit logging", "Compliance tools"],
    price: "$799/mo",
    icon: Shield,
  },
  {
    title: "ScaleForce",
    description: "Cloud-native platform designed for massive scalability and performance.",
    features: ["Auto-scaling", "Load balancing", "Global CDN"],
    price: "$1,299/mo",
    icon: Rocket,
  },
];

export default function Products() {
  return (
    <main className="relative min-h-screen bg-zinc-950 text-white">
      <CyberpunkBackground />
      <Navbar />
      <CloseButton />

      <div className="relative z-10 pt-20 pb-32 px-6">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-6xl md:text-7xl font-bold mb-8 glitch-text" data-text="Our Products">
            Our Products
          </h1>
          <p className="text-xl md:text-2xl text-zinc-300 mb-16 max-w-3xl cyber-text">
            Cutting-edge AI solutions engineered for enterprise performance and scalability.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {products.map((product, index) => (
              <div key={index} className="group animate-in" style={{ animationDelay: `${index * 150}ms` }}>
                <Card className="h-full border border-purple-500/20 bg-background/20 backdrop-blur-sm hover:border-purple-500/40 hover:bg-purple-500/10 transition-all duration-300">
                  <div className="p-8">
                    <div className="mb-6 p-3 w-12 h-12 rounded-lg bg-purple-500/20 flex items-center justify-center group-hover:bg-purple-500/30 transition-colors">
                      <product.icon className="h-6 w-6 text-purple-400" />
                    </div>
                    <h3 className="text-2xl font-semibold mb-2 cyber-text">{product.title}</h3>
                    <p className="text-zinc-400 mb-6 group-hover:text-zinc-300 transition-colors">
                      {product.description}
                    </p>
                    <ul className="space-y-3 mb-8">
                      {product.features.map((feature, i) => (
                        <li key={i} className="flex items-center text-zinc-300">
                          <ArrowRight className="h-4 w-4 mr-2 text-purple-400" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                    <div className="flex items-center justify-between">
                      <span className="text-2xl font-bold text-purple-400">{product.price}</span>
                      <Button variant="secondary" className="group relative bg-purple-500/20 hover:bg-purple-500/30 border border-purple-500/40">
                        Learn More
                        <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                      </Button>
                    </div>
                  </div>
                </Card>
              </div>
            ))}
          </div>
        </div>
      </div>
    </main>
  );
}