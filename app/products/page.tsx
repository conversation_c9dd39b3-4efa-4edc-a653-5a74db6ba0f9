import { CyberpunkBackground } from "@/components/cyberpunk-background";
import { Navbar } from "@/components/navbar";
import { CloseButton } from "@/components/close-button";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON>, Bot, Share2, Code, Shield } from "lucide-react";
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'AI Solutions - vruha studio',
  description: 'Comprehensive AI automation solution packages: business automation, social media management, rapid development, and security automation services.',
  keywords: ['AI solutions', 'automation packages', 'business automation', 'social media automation', 'rapid development', 'security automation', 'vruha studio'],
  openGraph: {
    title: 'AI Solutions - vruha studio',
    description: 'Comprehensive AI automation solution packages designed for modern businesses.',
    url: '/products',
  },
  twitter: {
    title: 'AI Solutions - vruha studio',
    description: 'Comprehensive AI automation solution packages designed for modern businesses.',
  },
};

const products = [
  {
    title: "Business Automation Suite",
    description: "Complete AI agent solution to automate boring business processes and eliminate repetitive tasks.",
    features: ["Custom AI agents", "Process optimization", "Time & cost savings", "24/7 automation"],
    price: "Custom Quote",
    icon: Bot,
  },
  {
    title: "Social Media Automation",
    description: "Prebuilt AI agents for comprehensive social media management and online presence monitoring.",
    features: ["Content automation", "Engagement monitoring", "Ad campaign optimization", "Multi-platform support"],
    price: "From $299/mo",
    icon: Share2,
  },
  {
    title: "Rapid Development Package",
    description: "Fast-track software development from MVP to complex solutions using cutting-edge AI tools.",
    features: ["AI-powered development", "MVP to production", "Custom solutions", "Expert consultation"],
    price: "Project-based",
    icon: Code,
  },
  {
    title: "Security Automation",
    description: "AI-powered infrastructure security with intelligent monitoring, alerts, and asset management.",
    features: ["Infrastructure monitoring", "Threat detection", "Alert systems", "Security dashboards"],
    price: "From $599/mo",
    icon: Shield,
  },
];

export default function Products() {
  return (
    <main className="relative min-h-screen bg-zinc-950 text-white">
      <CyberpunkBackground />
      <Navbar />
      <CloseButton />

      <div className="relative z-10 pt-20 pb-32 px-6">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-6xl md:text-7xl font-bold mb-8 glitch-text" data-text="AI Solutions">
            AI Solutions
          </h1>
          <p className="text-xl md:text-2xl text-zinc-300 mb-16 max-w-3xl cyber-text">
            Comprehensive AI automation packages designed to transform your business operations and drive efficiency.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {products.map((product, index) => (
              <div key={index} className="group animate-in" style={{ animationDelay: `${index * 150}ms` }}>
                <Card className="h-full border border-purple-500/20 bg-background/20 backdrop-blur-sm hover:border-purple-500/40 hover:bg-purple-500/10 transition-all duration-300">
                  <div className="p-8">
                    <div className="mb-6 p-3 w-12 h-12 rounded-lg bg-purple-500/20 flex items-center justify-center group-hover:bg-purple-500/30 transition-colors">
                      <product.icon className="h-6 w-6 text-purple-400" />
                    </div>
                    <h3 className="text-2xl font-semibold mb-2 cyber-text">{product.title}</h3>
                    <p className="text-zinc-400 mb-6 group-hover:text-zinc-300 transition-colors">
                      {product.description}
                    </p>
                    <ul className="space-y-3 mb-8">
                      {product.features.map((feature, i) => (
                        <li key={i} className="flex items-center text-zinc-300">
                          <ArrowRight className="h-4 w-4 mr-2 text-purple-400" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                    <div className="flex items-center justify-between">
                      <span className="text-2xl font-bold text-purple-400">{product.price}</span>
                      <Button variant="secondary" className="group relative bg-purple-500/20 hover:bg-purple-500/30 border border-purple-500/40">
                        Learn More
                        <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                      </Button>
                    </div>
                  </div>
                </Card>
              </div>
            ))}
          </div>
        </div>
      </div>
    </main>
  );
}