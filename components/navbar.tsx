"use client";

import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Phone, Box, Users, Sparkles } from "lucide-react";

export function Navbar() {
  const router = useRouter();

  return (
    <div className="fixed bottom-8 left-0 right-0 z-50 flex justify-center gap-4">
      <Button
        variant="secondary"
        className="group relative bg-background/20 backdrop-blur-sm border border-blue-500/30 hover:bg-blue-500/20 hover:border-blue-500/50 transition-all duration-300"
        onClick={() => router.push('/services')}
      >
        <div className="absolute inset-0 bg-blue-500/20 group-hover:bg-blue-500/30 transform skew-x-12 transition-all duration-300" />
        <Sparkles className="w-4 h-4 mr-2 relative z-10" />
        <span className="relative z-10">Services</span>
      </Button>

      <Button
        variant="secondary"
        className="group relative bg-background/20 backdrop-blur-sm border border-purple-500/30 hover:bg-purple-500/20 hover:border-purple-500/50 transition-all duration-300"
        onClick={() => router.push('/products')}
      >
        <div className="absolute inset-0 bg-purple-500/20 group-hover:bg-purple-500/30 transform skew-x-12 transition-all duration-300" />
        <Box className="w-4 h-4 mr-2 relative z-10" />
        <span className="relative z-10">Products</span>
      </Button>

      <Button
        variant="secondary"
        className="group relative bg-background/20 backdrop-blur-sm border border-cyan-500/30 hover:bg-cyan-500/20 hover:border-cyan-500/50 transition-all duration-300"
        onClick={() => router.push('/about')}
      >
        <div className="absolute inset-0 bg-cyan-500/20 group-hover:bg-cyan-500/30 transform skew-x-12 transition-all duration-300" />
        <Users className="w-4 h-4 mr-2 relative z-10" />
        <span className="relative z-10">About Us</span>
      </Button>

      <Button
        variant="secondary"
        className="group relative bg-background/20 backdrop-blur-sm border border-green-500/30 hover:bg-green-500/20 hover:border-green-500/50 transition-all duration-300"
        onClick={() => router.push('/contact')}
      >
        <div className="absolute inset-0 bg-green-500/20 group-hover:bg-green-500/30 transform skew-x-12 transition-all duration-300" />
        <Phone className="w-4 h-4 mr-2 relative z-10" />
        <span className="relative z-10">Contact Us</span>
      </Button>
    </div>
  );
}