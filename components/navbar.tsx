"use client";

import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Phone, Box, Users, Sparkles } from "lucide-react";

export function Navbar() {
  const router = useRouter();

  return (
    <div className="fixed bottom-8 left-0 right-0 z-50 flex justify-center gap-4">
      <Button
        variant="secondary"
        className="group relative bg-gray-900/80 backdrop-blur-sm border border-gray-700 hover:bg-gray-800 hover:border-gray-600 transition-all duration-300 text-white"
        onClick={() => router.push('/services')}
      >
        <Sparkles className="w-4 h-4 mr-2" />
        <span>Services</span>
      </Button>

      <Button
        variant="secondary"
        className="group relative bg-gray-900/80 backdrop-blur-sm border border-gray-700 hover:bg-gray-800 hover:border-gray-600 transition-all duration-300 text-white"
        onClick={() => router.push('/products')}
      >
        <Box className="w-4 h-4 mr-2" />
        <span>Solutions</span>
      </Button>

      <Button
        variant="secondary"
        className="group relative bg-gray-900/80 backdrop-blur-sm border border-gray-700 hover:bg-gray-800 hover:border-gray-600 transition-all duration-300 text-white"
        onClick={() => router.push('/about')}
      >
        <Users className="w-4 h-4 mr-2" />
        <span>About Us</span>
      </Button>

      <Button
        variant="secondary"
        className="group relative bg-gray-900/80 backdrop-blur-sm border border-gray-700 hover:bg-gray-800 hover:border-gray-600 transition-all duration-300 text-white"
        onClick={() => router.push('/contact')}
      >
        <Phone className="w-4 h-4 mr-2" />
        <span>Contact Us</span>
      </Button>
    </div>
  );
}