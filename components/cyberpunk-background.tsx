"use client";

import { useEffect, useState } from 'react';

export function CyberpunkBackground() {
    const [scrollY, setScrollY] = useState(0);

    useEffect(() => {
        const handleScroll = () => {
            setScrollY(window.scrollY);
        };

        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, []);

    const scrollProgress = Math.min(scrollY / 2000, 1); // Normalize scroll to 0-1

    return (
        <div
            className="fixed inset-0 pointer-events-none z-0 transition-all duration-500 ease-out"
            style={{
                background: `
          linear-gradient(
            135deg, 
            rgba(15, 15, 15, 1) 0%,
            rgba(25, 25, 35, ${0.95 + scrollProgress * 0.05}) 30%,
            rgba(20, 20, 30, ${0.98 + scrollProgress * 0.02}) 60%,
            rgba(10, 10, 10, 1) 100%
          )
        `
            }}
        />
    );
} 