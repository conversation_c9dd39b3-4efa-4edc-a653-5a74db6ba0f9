"use client";

import { useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

export function HeroSection() {
  const containerRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!containerRef.current) return;
      
      const { clientX, clientY } = e;
      const rect = containerRef.current.getBoundingClientRect();
      
      const x = (clientX - rect.left) / rect.width;
      const y = (clientY - rect.top) / rect.height;
      
      const title = containerRef.current.querySelector('.hero-title');
      const subtitle = containerRef.current.querySelector('.hero-subtitle');
      
      if (title && subtitle) {
        const titleX = x * 10;
        const titleY = y * 10;
        const subtitleX = x * 5;
        const subtitleY = y * 5;
        
        (title as HTMLElement).style.transform = `translate(${titleX}px, ${titleY}px)`;
        (subtitle as HTMLElement).style.transform = `translate(${subtitleX}px, ${subtitleY}px)`;
      }
    };
    
    const container = containerRef.current;
    if (container) {
      container.addEventListener('mousemove', handleMouseMove);
    }
    
    return () => {
      if (container) {
        container.removeEventListener('mousemove', handleMouseMove);
      }
    };
  }, []);

  const scrollToContact = () => {
    const contactSection = document.getElementById('contact');
    if (contactSection) {
      contactSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section className="relative min-h-screen flex items-center pt-20">
      <div 
        ref={containerRef}
        className="container relative z-10 py-20 md:py-32"
      >
        <div className="max-w-3xl mx-auto text-center">
          <h1 className="hero-title transition-transform duration-300 text-4xl sm:text-5xl md:text-6xl font-bold tracking-tight mb-6">
            Intelligent Automation for the <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-500 to-violet-500">AI-Powered Future</span>
          </h1>
          <p className="hero-subtitle transition-transform duration-300 text-xl md:text-2xl text-muted-foreground mb-12 max-w-2xl mx-auto">
            Harness the power of intelligent agents to transform your business processes and unlock new possibilities.
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
            <Button size="lg" onClick={scrollToContact} className="group">
              Get Started
              <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Button>
            <Button size="lg" variant="outline">
              Explore Solutions
            </Button>
          </div>
        </div>
        
        <div className="mt-24 flex justify-center gap-8 md:gap-16 opacity-70">
          <div className="flex flex-col items-center">
            <div className="text-3xl md:text-4xl font-bold">250+</div>
            <div className="text-sm text-muted-foreground">Clients</div>
          </div>
          <div className="flex flex-col items-center">
            <div className="text-3xl md:text-4xl font-bold">95%</div>
            <div className="text-sm text-muted-foreground">Satisfaction</div>
          </div>
          <div className="flex flex-col items-center">
            <div className="text-3xl md:text-4xl font-bold">24/7</div>
            <div className="text-sm text-muted-foreground">Support</div>
          </div>
        </div>
      </div>
    </section>
  );
}