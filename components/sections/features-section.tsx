"use client";

import { useRef, useEffect } from "react";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { CheckCircle } from "lucide-react";

const features = [
  {
    title: "Advanced Natural Language Processing",
    description: "Our AI understands context, intent, and nuance in human language for more natural interactions.",
  },
  {
    title: "Adaptive Learning Algorithms",
    description: "Systems that continuously learn from data and user interactions to improve performance over time.",
  },
  {
    title: "Real-time Decision Engine",
    description: "Process complex decision trees in milliseconds to provide immediate, intelligent responses.",
  },
  {
    title: "Enterprise-grade Security",
    description: "End-to-end encryption and compliance with industry standards to keep your data safe.",
  },
  {
    title: "Seamless Multi-platform Support",
    description: "Deploy across web, mobile, and desktop with consistent performance and user experience.",
  },
];

export function FeaturesSection() {
  const sectionRef = useRef<HTMLDivElement>(null);
  const featureItemsRef = useRef<(HTMLDivElement | null)[]>([]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-in");
          }
        });
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    featureItemsRef.current.forEach((item) => {
      if (item) observer.observe(item);
    });

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
      featureItemsRef.current.forEach((item) => {
        if (item) observer.unobserve(item);
      });
    };
  }, []);

  return (
    <section id="features" className="py-20 md:py-32 bg-muted/30">
      <div className="container">
        <div className="grid md:grid-cols-2 gap-12 items-center">
          <div 
            ref={sectionRef}
            className="opacity-0 translate-y-8 transition-all duration-700 ease-out"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Powerful Features for Modern Businesses
            </h2>
            <p className="text-xl text-muted-foreground mb-8">
              Our platform combines cutting-edge technology with intuitive design to deliver exceptional results.
            </p>
            
            <div className="space-y-4">
              {features.map((feature, index) => (
                <div
                  key={index}
                  ref={(el) => (featureItemsRef.current[index] = el)}
                  className={cn(
                    "flex gap-4 opacity-0 translate-y-8 transition-all duration-700 ease-out",
                    `delay-[${index * 100 + 300}ms]`
                  )}
                >
                  <CheckCircle className="h-6 w-6 text-primary shrink-0 mt-1" />
                  <div>
                    <h3 className="font-medium text-lg">{feature.title}</h3>
                    <p className="text-muted-foreground">{feature.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          <div className="relative h-[400px] md:h-[500px] rounded-lg overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-lg transform rotate-2 scale-105"></div>
            <div className="absolute inset-0 backdrop-blur-[1px] rounded-lg">
              <Image
                src="https://images.pexels.com/photos/2582937/pexels-photo-2582937.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2"
                alt="AI Visualization"
                fill
                className="object-cover rounded-lg"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-background/80 to-transparent rounded-lg"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}