"use client";

import { useEffect, useRef } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Bot, Workflow, Database, Zap, Bar<PERSON>hart, CloudCog } from "lucide-react";
import { cn } from "@/lib/utils";

const services = [
  {
    title: "AI Agents",
    description: "Autonomous AI agents that learn, adapt, and execute complex tasks across your organization.",
    icon: Bo<PERSON>,
  },
  {
    title: "Workflow Automation",
    description: "Streamline repetitive tasks and build intelligent workflows that scale with your business.",
    icon: Workflow,
  },
  {
    title: "Data Processing",
    description: "Transform raw data into actionable insights with our intelligent processing pipeline.",
    icon: Database,
  },
  {
    title: "API Integration",
    description: "Seamlessly connect with existing systems and third-party services for unified operations.",
    icon: Zap,
  },
  {
    title: "Analytics Dashboard",
    description: "Visualize performance metrics and gain insights with customizable real-time dashboards.",
    icon: Bar<PERSON><PERSON>,
  },
  {
    title: "Cloud Solutions",
    description: "Scalable and secure cloud infrastructure optimized for AI and automation workloads.",
    icon: CloudCog,
  },
];

export function ServicesSection() {
  const sectionRef = useRef<HTMLDivElement>(null);
  const cardsRef = useRef<(HTMLDivElement | null)[]>([]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-in");
          }
        });
      },
      { threshold: 0.1 }
    );

    cardsRef.current.forEach((card) => {
      if (card) observer.observe(card);
    });

    return () => {
      cardsRef.current.forEach((card) => {
        if (card) observer.unobserve(card);
      });
    };
  }, []);

  return (
    <section id="services" className="py-20 md:py-32 relative">
      <div className="container">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Our Services</h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Comprehensive solutions designed to transform your business through intelligent automation.
          </p>
        </div>

        <div 
          ref={sectionRef} 
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        >
          {services.map((service, index) => (
            <div
              key={index}
              ref={(el) => (cardsRef.current[index] = el)}
              className={cn(
                "opacity-0 translate-y-8 transition-all duration-700 ease-out",
                `delay-[${index * 100}ms]`
              )}
            >
              <Card className="h-full border border-border/40 bg-card/50 backdrop-blur-sm hover:border-primary/20 hover:shadow-lg transition-all duration-300">
                <CardHeader>
                  <div className="mb-4 p-3 w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center">
                    <service.icon className="h-6 w-6 text-primary" />
                  </div>
                  <CardTitle>{service.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">{service.description}</CardDescription>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}