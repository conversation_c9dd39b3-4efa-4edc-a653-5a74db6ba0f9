"use client";

import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { X } from "lucide-react";

export function CloseButton() {
  const router = useRouter();

  return (
    <Button
      variant="ghost"
      size="icon"
      className="fixed top-6 right-6 z-50 bg-background/20 backdrop-blur-sm border border-border/40 hover:bg-background/40 transition-all duration-300"
      onClick={() => router.push("/")}
    >
      <X className="h-5 w-5" />
      <span className="sr-only">Close</span>
    </Button>
  );
}