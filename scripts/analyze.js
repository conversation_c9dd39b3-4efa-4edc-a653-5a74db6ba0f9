#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Simple bundle analyzer for static export
console.log('🔍 Analyzing NexusAI bundle size...\n');

function getDirectorySize(dirPath) {
    let totalSize = 0;
    const files = fs.readdirSync(dirPath);

    files.forEach(file => {
        const filePath = path.join(dirPath, file);
        const stats = fs.statSync(filePath);

        if (stats.isDirectory()) {
            totalSize += getDirectorySize(filePath);
        } else {
            totalSize += stats.size;
        }
    });

    return totalSize;
}

function formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Check if out directory exists (after build)
const outDir = path.join(process.cwd(), 'out');

if (fs.existsSync(outDir)) {
    const totalSize = getDirectorySize(outDir);
    console.log(`📦 Total bundle size: ${formatBytes(totalSize)}`);

    // Analyze specific directories
    const subdirs = ['_next', 'assets'];
    subdirs.forEach(subdir => {
        const subdirPath = path.join(outDir, subdir);
        if (fs.existsSync(subdirPath)) {
            const size = getDirectorySize(subdirPath);
            console.log(`   📁 ${subdir}: ${formatBytes(size)}`);
        }
    });

    console.log('\n✅ Analysis complete!');
    console.log('\n💡 Performance tips:');
    console.log('   • Keep bundle size under 244KB for initial load');
    console.log('   • Use lazy loading for non-critical components');
    console.log('   • Optimize images and use WebP format');
    console.log('   • Consider code splitting for large pages');

} else {
    console.log('❌ No build output found. Run "npm run build" first.');
    console.log('\n📋 Available commands:');
    console.log('   npm run build    - Build the application');
    console.log('   npm run start    - Start production server');
    console.log('   npm run dev      - Start development server');
} 